# 🐱 通用配置文件 - 多PCells类型混合专用

# ===== 芯片基本信息 =====
chip:
  name: "mixed_pcells_chip"
  size: [15000.0, 12000.0]  # [宽度, 高度] µm

# ===== PCells库定义 =====
pcells:
  waveguide_tip:
    component: "waveguide_tip_with_ppln_electrodes"
    defaults:
      wg_width: 2
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      bend_radius: 100
      period: 4.66
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5
      
  simple_electrode:
    component: "ppln_electrode"
    defaults:
      width: 50
      electrode_width: 100
      electrode_gap: 10
      fillet_radius: 5
      
  custom_waveguide:
    component: "waveguide_straight"
    defaults:
      width: 2
      length: 1000
      layer: "WG"

# ===== 布局设置（集中管理）=====
layout:
  # 网格设置
  grid:
    rows: 3                    # 行数
    cols: 6                    # 列数  
    row_spacing: 300           # 行间距 µm
    col_spacing: 0             # 列间距 µm（紧挨着）
    
  # 标签设置
  labels:
    enabled: true
    format: "{col_letter}{row_number}"  # A1, B2格式
    default_offset: 30                  # 默认标签偏移
    
  # smallpart内部布局
  internal:
    default_spacing: -250      # 默认smallpart内部组件间距
    default_count: 8           # 默认每个smallpart的组件数量
    
  # 列定义（按列组织PCells类型和参数）
  columns:
    # 第1列：波导长度扫描
    col_0:
      pcell_type: "waveguide_tip"
      description: "波导长度扫描"
      base_params: {poling_length: 500, duty_cycle: 0.2, gap_to_wg: 5.0, offset: -300}
      variations:
        - {wg_length: 800}
        - {wg_length: 1000}  
        - {wg_length: 1200}
      position_offset: [250, 0]
        
    # 第2列：极化长度扫描
    col_1:
      pcell_type: "waveguide_tip"
      description: "极化长度扫描"
      base_params: {wg_length: 1200, duty_cycle: 0.2, gap_to_wg: 5.0, offset: -300}
      variations:
        - {poling_length: 500}
        - {poling_length: 1000}
        - {poling_length: 1500}
      position_offset: [0, 0]
        
    # 第3列：占空比扫描
    col_2:
      pcell_type: "waveguide_tip"
      description: "占空比扫描"
      base_params: {wg_length: 1200, poling_length: 1000, gap_to_wg: 5.0, offset: -300}
      variations:
        - {duty_cycle: 0.2}
        - {duty_cycle: 0.25}
        - {duty_cycle: 0.3}
      position_offset: [-100, 0]
        
    # 第4列：电极间距扫描
    col_3:
      pcell_type: "waveguide_tip"
      description: "电极间距扫描"
      base_params: {wg_length: 1200, poling_length: 1000, duty_cycle: 0.25, offset: -300}
      variations:
        - {gap_to_wg: 5.0}
        - {gap_to_wg: 7.5}
        - {gap_to_wg: 10.0}
      position_offset: [-200, 0]
        
    # 第5列：简单电极
    col_4:
      pcell_type: "simple_electrode"
      description: "电极参数扫描"
      base_params: {electrode_gap: 10, fillet_radius: 5}
      variations:
        - {width: 50, electrode_width: 100}
        - {width: 75, electrode_width: 150}
        - {width: 100, electrode_width: 200}
      position_offset: [-300, 0]
      layout_override: {count: 6, label_offset: 50}
        
    # 第6列：自定义波导
    col_5:
      pcell_type: "custom_waveguide"
      description: "波导长度扫描"
      base_params: {width: 2, layer: "WG"}
      variations:
        - {length: 800}
        - {length: 1200}
        - {length: 1600}
      position_offset: [-400, 0]
      layout_override: {count: 4, label_offset: 80, internal_spacing: -100}

# ===== 标记设置 =====
marks:
  # 对准标记
  alignment:
    enabled: true
    layer: "MARK"
    margins: [1500, 1000, 1000, 1500]  # [xx, xy, yx, yy]
    spacing: 1000
    cross_size: [50, 2]  # [length, width]
    
  # 切割标记
  dicing:
    smallpart_enabled: true
    chip_enabled: true
    layer: "DICING"
    mark_size: [50, 20]
    boundary_offset: [-200, 200]

# ===== 输出设置 =====
output:
  path: "mixed_pcells_chip.gds"
  show_gds: true
