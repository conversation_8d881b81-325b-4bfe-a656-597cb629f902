
import gdsfactory as gf
from gdsfactory.typings import LayerSpec, CrossSectionSpec
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from pdk.layers import LAYER


@gf.cell
def bend_s_offset(
    offset: float = 40.0,
    radius: float = 20.0,
    cross_section: CrossSectionSpec = "strip",
    with_euler: bool = True,
) -> gf.Component:
    """创建S形弯曲，由两个弯曲和可选的直线段组成。

    Args:
        offset: 垂直偏移量 (µm)，正值向上，负值向下
        radius: 弯曲半径 (µm)
        cross_section: 截面规格
        with_euler: 是否使用euler弯曲代替圆弧弯曲

    Returns:
        gf.Component: S形弯曲组件
    """
    c = gf.Component()

    dy = abs(offset) - 2 * radius
    dy = max(0, dy)

    bend_function = gf.components.bend_euler if with_euler else gf.components.bend_circular
    bend = bend_function(angle=90, radius=radius, cross_section=cross_section)
    b1_ref = c.add_ref(bend)
    b2_ref = c.add_ref(bend)
    if dy > 0:
        straight = gf.components.straight(length=dy, cross_section=cross_section)
        s_ref = c.add_ref(straight)
        b1_ref.connect("o2", s_ref.ports["o1"])
        b2_ref.connect("o2", s_ref.ports["o2"])
    else:
        b2_ref.connect("o2", b1_ref.ports["o2"])
    c.add_port("o1", port=b1_ref.ports["o1"])
    c.add_port("o2", port=b2_ref.ports["o1"])
    if offset==0:
        c=gf.components.straight(length=2*radius, cross_section=cross_section)
    #c.draw_ports()
    return c


@gf.cell
def waveguide_with_tips(
    wg_length: float = 100.0,
    wg_width: float = 0.5,
    tip_length: float = 20.0,
    tip_width: float = 0.2,
    taper_length: float = 15.0,
    offset: float = 40.0,
    bend_radius: float = 10.0,
    layer: LayerSpec = LAYER.WG,

) -> gf.Component:
    """创建带有左右两个tip的波导，使用euler弯曲和绝热taper连接。
    
    Args:
        wg_length: 中央波导长度
        wg_width: 波导宽度
        tip_length: tip长度
        tip_width: tip宽度
        taper_length: taper长度
        left_offset: 左边tip的垂直偏移（负值向上）
        right_offset: 右边tip的垂直偏移（负值向上）
        bend_radius: euler弯曲的半径
        layer: 波导层
        center_waveguide: 是否将中央波导居中显示
    
    Returns:
        gf.Component: 波导tip组件
    """
    c = gf.Component()
    
    # 创建cross sections
    xs = gf.cross_section.strip(width=wg_width, layer=layer)
    xs_tip = gf.cross_section.strip(width=tip_width, layer=layer)
    
    # 1. 创建中央波导
    central_wg = gf.components.straight(
        length=wg_length,
        cross_section=xs
    )
    # 2. 创建S形弯曲
    bend = bend_s_offset(
        offset=offset,
        radius=bend_radius,
        cross_section=xs,
        with_euler=True
    )
    # 3. 创建taper
    taper = gf.components.taper_cross_section(
        length=taper_length,
        cross_section1=xs_tip,
        cross_section2=xs
    )
    # 4. 创建tip
    tip = gf.components.straight(
        length=tip_length,
        cross_section=xs_tip
    )
    # 5. 连接所有组件
    # 连接中央波导和S形弯曲
   
    central_wg_ref = c.add_ref(central_wg)
    left_s_bend_ref = c.add_ref(bend)
    right_s_bend_ref = c.add_ref(bend)
    left_taper_ref = c.add_ref(taper)
    right_taper_ref = c.add_ref(taper)
    left_tip_ref = c.add_ref(tip)
    right_tip_ref = c.add_ref(tip)

    left_s_bend_ref.connect("o2", central_wg_ref.ports["o1"])
    left_taper_ref.connect("o2", left_s_bend_ref.ports["o1"])
    left_tip_ref.connect("o2", left_taper_ref.ports["o1"])
    
    right_s_bend_ref.connect("o1",central_wg_ref.ports["o2"])
    right_taper_ref.connect("o2", right_s_bend_ref.ports["o2"])
    right_tip_ref.connect("o1", right_taper_ref.ports["o1"])
    # 6. 添加端口
    c.add_port("o1", port=left_tip_ref.ports["o1"])
    c.add_port("o2", port=right_tip_ref.ports["o2"])
    #c.draw_ports()
    return c

if __name__ == "__main__":
    print("测试波导tip组件...")

    
    c = waveguide_with_tips(
        wg_length=100.0,
        wg_width=0.5,
        tip_length=20.0,
        tip_width=0.2,
        taper_length=15.0,
        left_offset=40.0,
        right_offset=40.0,
        bend_radius=10.0,
        layer=LAYER.WG,
   
    )
   
    # 显示组件
    c.show()
    print("\n测试完成!")



   