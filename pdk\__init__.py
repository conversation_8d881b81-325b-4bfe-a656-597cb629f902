"""PDK package for GDS cursor."""

import gdsfactory as gf
import inspect
from pathlib import Path
import importlib

from gdsfactory.typings import Layer, LayerSpec, ComponentSpec, CrossSectionSpec
from gdsfactory.component import Component
from gdsfactory.technology import LayerViews, LayerView

# 从相应模块导入定义
from pdk.layers import LAYER
from pdk import cross_sections

# -----------------
# 组件发现函数
# -----------------
def get_component_factories():
    """
    手动导入并注册所有组件。这是最可靠的方法。
    """
    # 关键修复: 在函数内部导入组件模块
    from components.waveguide_with_electrodes import waveguide_tip_with_ppln_electrodes
    from components.alignment_marks import alignment_cross
    from components.text_labels import simple_text_label
    from components.dicing_marks import dicing_rectangle_mark, chip_dicing_grid
    # ... 在这里添加其他需要注册的组件导入 ...

    # 手动构建组件字典
    component_factories = {
        "waveguide_tip_with_ppln_electrodes": waveguide_tip_with_ppln_electrodes,
        "alignment_cross": alignment_cross,
        "simple_text_label": simple_text_label,
        "dicing_rectangle_mark": dicing_rectangle_mark,
        "chip_dicing_grid": chip_dicing_grid,
        # ... 在这里添加其他组件到字典 ...
    }
    
    print("Registered components:", list(component_factories.keys()))
    return component_factories

# --- 创建一个干净的 LayerViews ---
# 这可以防止gdsfactory加载默认的、包含很多层的视图
layer_views = LayerViews()

# 定义层的颜色和样式
layer_colors = {
    'SUBSTRATE': '#c0c0c0',
    'WG': '#4040ff', 
    'METAL': '#ffd400',
    'HEATER': '#ff8080',
    'DICING': '#80c080',
    'TEXT': '#804080',
    'MARK': '#c0c040'
}

# 只添加我们自己定义的层到视图中
defined_layers = []

# 直接使用我们知道的层定义
layer_definitions = {
    'SUBSTRATE': (0, 0),
    'WG': (1, 0),
    'METAL': (41, 0),
    'HEATER': (47, 0),
    'DICING': (5, 0),
    'TEXT': (66, 0),
    'MARK': (101, 0)
}

for layer_name, layer_value in layer_definitions.items():
    color = layer_colors.get(layer_name, '#808080')  # 默认灰色
    layer_views.layer_views[layer_name] = LayerView(
        layer=layer_value,
        color=color
    )
    defined_layers.append(layer_name)

print(f"Defined layers found: {defined_layers}")

# 直接使用构造函数创建PDK
PDK = gf.Pdk(
    name="MyPDK",
    layers=LAYER,
    layer_views=layer_views,  # 显式传递干净的视图
    cross_sections={
        name: func
        for name, func in inspect.getmembers(cross_sections, inspect.isfunction)
        if inspect.signature(func).return_annotation == gf.CrossSection
    },
    cells=get_component_factories(),
)

PDK.activate()

# 简化版的层检查
print(f"PDK activated with {len(defined_layers)} custom layers: {defined_layers}")

# 导出干净的层对象供全局使用
LAYERS = PDK.layers

__all__ = [
    "PDK",
    "LAYERS",
    "Layer",
    "LayerSpec",
    "Component",
    "ComponentSpec",
    "CrossSectionSpec",
]
